package by.algin.webuiservice.client;

import by.algin.constants.CommonPathConstants;
import by.algin.constants.CommonServiceConstants;
import by.algin.dto.response.ApiResponse;
import by.algin.dto.response.UserResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Feign client interface for UserService communication.
 * Separated from UserServiceClient for better maintainability and reusability.
 */
@FeignClient(
    name = CommonServiceConstants.USER_SERVICE, 
    contextId = "${app.feign-clients.user-service.context-id}", 
    path = "${app.feign-clients.user-service.path}"
)
public interface UserServiceFeignClient {

    @GetMapping(CommonPathConstants.API_USERS_SEARCH)
    ApiResponse<UserResponse> searchUsers(@RequestParam(CommonPathConstants.PARAM_FIELD) String field,
                                        @RequestParam(CommonPathConstants.PARAM_VALUE) String value);
}
