# WebUIService Configuration
# This file contains configuration for Feign clients to eliminate hardcoded values

app:
  feign-clients:
    project-service:
      path: "/api"
      context-id: "project-service-webui"
    user-service:
      path: "/api"
      context-id: "user-service-webui"
    auth-service:
      path: "/api/auth"
      context-id: "auth-service-webui"
  
  security:
    jwt:
      secret-key: ${JWT_SECRET_KEY:your-default-secret-key-change-in-production}
      default-expiry-seconds: ${JWT_ACCESS_TOKEN_EXPIRATION_SECONDS:900}
      refresh-token:
        expiry-seconds: ${JWT_REFRESH_TOKEN_EXPIRATION_SECONDS:604800}
    cookies:
      jwt-cookie-name: "jwt-token"
      refresh-jwt-cookie-name: "refresh-jwt-token"
      jsession-id-cookie-name: "JSESSIONID"
  
  locale:
    cache-seconds: -1

# Spring Boot Configuration
spring:
  application:
    name: WEB-UI-SERVICE
  
  # Thymeleaf Configuration
  thymeleaf:
    cache: false
    encoding: UTF-8
    mode: HTML
    prefix: classpath:/templates/
    suffix: .html
  
  # Security Configuration
  security:
    user:
      name: admin
      password: admin
      roles: ADMIN

# Server Configuration
server:
  port: 8082
  servlet:
    context-path: /
    encoding:
      charset: UTF-8
      enabled: true
      force: true

# Eureka Configuration
eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    register-with-eureka: true
    fetch-registry: true
  instance:
    prefer-ip-address: true
    hostname: localhost

# Feign Configuration
feign:
  client:
    config:
      default:
        connect-timeout: 5000
        read-timeout: 10000
        logger-level: basic
  compression:
    request:
      enabled: true
    response:
      enabled: true

# Logging Configuration
logging:
  level:
    by.algin.webuiservice: DEBUG
    org.springframework.security: DEBUG
    feign: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
