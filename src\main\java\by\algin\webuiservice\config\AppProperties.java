package by.algin.webuiservice.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "app")
@Getter
public class AppProperties {
    private final Security security;
    private final LocaleSettings locale;
    private final FeignClients feignClients;

    public AppProperties() {
        this.security = new Security();
        this.locale = new LocaleSettings();
        this.feignClients = new FeignClients();
    }

    @Getter
    @Setter
    public static class Security {
        private Jwt jwt;
        private Cookies cookies;

        public Security() {
            this.jwt = new Jwt();
            this.cookies = new Cookies();
        }
    }

    @Getter
    @Setter
    public static class Jwt {
        private String secretKey;
        private int defaultExpirySeconds;
        private RefreshToken refreshToken;

        public Jwt() {
            this.refreshToken = new RefreshToken();
        }
    }

    @Getter
    @Setter
    public static class RefreshToken {
        private int expirySeconds;
    }

    @Getter
    @Setter
    public static class Cookies {
        private String jwtCookieName;
        private String refreshJwtCookieName;
        private String jsessionIdCookieName;
    }

    @Getter
    @Setter
    public static class LocaleSettings {
        private int cacheSeconds = -1;
    }

    @Getter
    @Setter
    public static class FeignClients {
        private ProjectService projectService;
        private UserService userService;
        private AuthService authService;

        public FeignClients() {
            this.projectService = new ProjectService();
            this.userService = new UserService();
            this.authService = new AuthService();
        }

        @Getter
        @Setter
        public static class ProjectService {
            private String path = "/api";
            private String contextId = "project-service-webui";
        }

        @Getter
        @Setter
        public static class UserService {
            private String path = "/api";
            private String contextId = "user-service-webui";
        }

        @Getter
        @Setter
        public static class AuthService {
            private String path = "/api/auth";
            private String contextId = "auth-service-webui";
        }
    }
}