package by.algin.webuiservice.client;

import by.algin.constants.CommonPathConstants;
import by.algin.constants.CommonServiceConstants;
import by.algin.dto.project.*;
import by.algin.dto.response.PagedResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * Feign client interface for ProjectService communication.
 * Separated from ProjectServiceClient for better maintainability and reusability.
 */
@FeignClient(
    name = CommonServiceConstants.PROJECT_SERVICE,
    contextId = "${app.feign-clients.project-service.context-id}",
    path = "${app.feign-clients.project-service.path}"
)
public interface ProjectServiceFeignClient {

    @PostMapping(CommonPathConstants.API_PROJECTS)
    ProjectResponse createProject(@RequestBody CreateProjectRequest request);

    @GetMapping(CommonPathConstants.API_PROJECTS_BY_ID)
    ProjectResponse getProject(@PathVariable("projectId") Long projectId);

    @PutMapping(CommonPathConstants.API_PROJECTS_BY_ID)
    ProjectResponse updateProject(@PathVariable("projectId") Long projectId, @RequestBody UpdateProjectRequest request);

    @DeleteMapping(CommonPathConstants.API_PROJECTS_BY_ID)
    void deleteProject(@PathVariable("projectId") Long projectId);

    @GetMapping(CommonPathConstants.API_PROJECTS_BY_USER)
    PagedResponse<ProjectResponse> getUserProjects(@RequestParam("username") String username);

    @PostMapping(CommonPathConstants.API_PROJECTS_MEMBERS)
    ProjectMemberResponse addProjectMember(@PathVariable("projectId") Long projectId, @RequestBody AddProjectMemberRequest request);

    @PutMapping(CommonPathConstants.API_PROJECTS_MEMBERS_ROLE)
    ProjectMemberResponse updateProjectMemberRole(@PathVariable("projectId") Long projectId,
                                                 @PathVariable("userId") Long userId,
                                                 @RequestBody UpdateProjectMemberRoleRequest request);

    @DeleteMapping(CommonPathConstants.API_PROJECTS_MEMBERS_DELETE)
    void removeProjectMember(@PathVariable("projectId") Long projectId, @PathVariable("userId") Long userId);

    @GetMapping(CommonPathConstants.API_PROJECTS_MEMBERS)
    ProjectMemberListResponse getProjectMembers(@PathVariable("projectId") Long projectId);

    @PostMapping(CommonPathConstants.API_PROJECTS_INVITATIONS)
    InvitationResponse createInvitation(@PathVariable("projectId") Long projectId, @RequestBody CreateInvitationRequest request);

    @GetMapping(CommonPathConstants.API_INVITATIONS_BY_TOKEN)
    InvitationResponse getInvitationByToken(@PathVariable("token") String token);

    @PostMapping(CommonPathConstants.API_INVITATIONS_ACCEPT)
    ProjectMemberResponse acceptInvitation(@PathVariable("token") String token);
}
