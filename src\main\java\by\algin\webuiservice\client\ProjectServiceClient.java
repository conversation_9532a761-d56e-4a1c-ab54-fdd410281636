package by.algin.webuiservice.client;

import by.algin.dto.project.*;
import by.algin.dto.response.PagedResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Slf4j
@Component
public class ProjectServiceClient {

    private final ProjectServiceFeignClient feignClient;

    public ProjectServiceClient(ProjectServiceFeignClient feignClient) {
        this.feignClient = feignClient;
    }

    public Optional<ProjectResponse> createProject(CreateProjectRequest request) {
        try {
            ProjectResponse response = feignClient.createProject(request);
            return Optional.of(response);
        } catch (Exception e) {
            log.error("Failed to create project: {}", e.getMessage());
            return Optional.empty();
        }
    }

    public Optional<ProjectResponse> getProject(String projectId) {
        try {
            Long id = Long.parseLong(projectId);
            ProjectResponse response = feignClient.getProject(id);
            return Optional.of(response);
        } catch (NumberFormatException e) {
            log.error("Invalid project ID format: {}", projectId);
            return Optional.empty();
        } catch (Exception e) {
            log.error("Failed to get project: {}", e.getMessage());
            return Optional.empty();
        }
    }

    public Optional<ProjectResponse> updateProject(String projectId, UpdateProjectRequest request) {
        try {
            Long id = Long.parseLong(projectId);
            ProjectResponse response = feignClient.updateProject(id, request);
            return Optional.of(response);
        } catch (NumberFormatException e) {
            log.error("Invalid project ID format: {}", projectId);
            return Optional.empty();
        } catch (Exception e) {
            log.error("Failed to update project: {}", e.getMessage());
            return Optional.empty();
        }
    }

    public boolean deleteProject(String projectId) {
        try {
            Long id = Long.parseLong(projectId);
            feignClient.deleteProject(id);
            return true;
        } catch (NumberFormatException e) {
            log.error("Invalid project ID format: {}", projectId);
            return false;
        } catch (Exception e) {
            log.error("Failed to delete project: {}", e.getMessage());
            return false;
        }
    }

    public Optional<PagedResponse<ProjectResponse>> getUserProjects(String username) {
        try {
            PagedResponse<ProjectResponse> response = feignClient.getUserProjects(username);
            return Optional.ofNullable(response);
        } catch (Exception e) {
            log.error("Failed to get projects for user: {}", e.getMessage());
            return Optional.empty();
        }
    }

    public Optional<ProjectMemberResponse> addProjectMember(String projectId, AddProjectMemberRequest request) {
        try {
            Long id = Long.parseLong(projectId);
            ProjectMemberResponse response = feignClient.addProjectMember(id, request);
            return Optional.of(response);
        } catch (NumberFormatException e) {
            log.error("Invalid project ID format: {}", projectId);
            return Optional.empty();
        } catch (Exception e) {
            log.error("Failed to add member to project: {}", e.getMessage());
            return Optional.empty();
        }
    }

    public Optional<ProjectMemberResponse> updateProjectMemberRole(String projectId, String userId, UpdateProjectMemberRoleRequest request) {
        try {
            Long projectIdLong = Long.parseLong(projectId);
            Long userIdLong = Long.parseLong(userId);
            ProjectMemberResponse response = feignClient.updateProjectMemberRole(projectIdLong, userIdLong, request);
            return Optional.of(response);
        } catch (NumberFormatException e) {
            log.error("Invalid ID format - projectId: {}, userId: {}", projectId, userId);
            return Optional.empty();
        } catch (Exception e) {
            log.error("Failed to update member role: {}", e.getMessage());
            return Optional.empty();
        }
    }

    public boolean removeProjectMember(String projectId, String userId) {
        try {
            Long projectIdLong = Long.parseLong(projectId);
            Long userIdLong = Long.parseLong(userId);
            feignClient.removeProjectMember(projectIdLong, userIdLong);
            return true;
        } catch (NumberFormatException e) {
            log.error("Invalid ID format - projectId: {}, userId: {}", projectId, userId);
            return false;
        } catch (Exception e) {
            log.error("Failed to remove member: {}", e.getMessage());
            return false;
        }
    }

    public Optional<ProjectMemberListResponse> getProjectMembers(String projectId) {
        try {
            Long id = Long.parseLong(projectId);
            ProjectMemberListResponse response = feignClient.getProjectMembers(id);
            return Optional.of(response);
        } catch (NumberFormatException e) {
            log.error("Invalid project ID format: {}", projectId);
            return Optional.empty();
        } catch (Exception e) {
            log.error("Failed to get project members: {}", e.getMessage());
            return Optional.empty();
        }
    }

    public Optional<InvitationResponse> createInvitation(Long projectId, CreateInvitationRequest request) {
        try {
            InvitationResponse response = feignClient.createInvitation(projectId, request);
            return Optional.ofNullable(response);
        } catch (Exception e) {
            log.error("Failed to create invitation: {}", e.getMessage());
            return Optional.empty();
        }
    }

    public Optional<InvitationResponse> getInvitationByToken(String token) {
        try {
            InvitationResponse response = feignClient.getInvitationByToken(token);
            return Optional.ofNullable(response);
        } catch (Exception e) {
            log.error("Failed to get invitation by token: {}", e.getMessage());
            return Optional.empty();
        }
    }

    public Optional<ProjectMemberResponse> acceptInvitation(String token) {
        try {
            ProjectMemberResponse response = feignClient.acceptInvitation(token);
            return Optional.ofNullable(response);
        } catch (Exception e) {
            log.error("Failed to accept invitation: {}", e.getMessage());
            return Optional.empty();
        }
    }

}
