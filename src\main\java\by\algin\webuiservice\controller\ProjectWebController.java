package by.algin.webuiservice.controller;

import by.algin.constants.CommonPathConstants;
import by.algin.constants.CommonTemplateConstants;
import by.algin.dto.project.*;
import by.algin.webuiservice.constants.PathConstants;
import by.algin.webuiservice.service.ProjectWebService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

@Slf4j
@Controller
@RequestMapping(CommonPathConstants.PROJECTS_ENDPOINT)
@RequiredArgsConstructor
public class ProjectWebController {

    private final ProjectWebService projectWebService;

    @GetMapping(CommonPathConstants.CREATE_ENDPOINT)
    public String showCreateForm(Model model) {
        model.addAttribute("project", new CreateProjectRequest());
        return CommonTemplateConstants.TEMPLATE_PROJECTS_CREATE;
    }

    @PostMapping(CommonPathConstants.CREATE_ENDPOINT)
    public String createProject(@Valid @ModelAttribute("project") CreateProjectRequest request,
                               BindingResult result,
                               Authentication authentication,
                               Model model,
                               RedirectAttributes redirectAttributes) {

        if (result.hasErrors()) {
            return CommonTemplateConstants.TEMPLATE_PROJECTS_CREATE;
        }

        boolean created = projectWebService.createProject(request, authentication, model);
        if (created) {
            redirectAttributes.addFlashAttribute("success", "Project created successfully!");
            return "redirect:" + PathConstants.DASHBOARD;
        } else {
            return CommonTemplateConstants.TEMPLATE_PROJECTS_CREATE;
        }
    }

    @GetMapping(CommonPathConstants.PROJECT_ID_ENDPOINT)
    public String viewProject(@PathVariable Long projectId,
                             Authentication authentication,
                             Model model) {

        projectWebService.prepareProjectView(projectId, authentication, model);
        return CommonTemplateConstants.TEMPLATE_PROJECTS_VIEW;
    }

    @GetMapping(CommonPathConstants.PROJECT_EDIT_ENDPOINT)
    public String showEditForm(@PathVariable Long projectId,
                              Authentication authentication,
                              Model model) {

        projectWebService.prepareProjectView(projectId, authentication, model);
        model.addAttribute("updateRequest", new UpdateProjectRequest());

        return CommonTemplateConstants.TEMPLATE_PROJECTS_EDIT;
    }

    @PostMapping(CommonPathConstants.PROJECT_EDIT_ENDPOINT)
    public String updateProject(@PathVariable Long projectId,
                               @Valid @ModelAttribute("updateRequest") UpdateProjectRequest request,
                               BindingResult result,
                               Authentication authentication,
                               Model model,
                               RedirectAttributes redirectAttributes) {

        if (result.hasErrors()) {
            projectWebService.prepareProjectView(projectId, authentication, model);
            return CommonTemplateConstants.TEMPLATE_PROJECTS_EDIT;
        }

        boolean updated = projectWebService.updateProject(projectId, request, authentication, model);
        if (updated) {
            redirectAttributes.addFlashAttribute("success", "Project updated successfully!");
            return CommonTemplateConstants.REDIRECT_PROJECTS_VIEW + projectId;
        } else {
            projectWebService.prepareProjectView(projectId, authentication, model);
            return CommonTemplateConstants.TEMPLATE_PROJECTS_EDIT;
        }
    }

    @PostMapping(CommonPathConstants.PROJECT_DELETE_ENDPOINT)
    public String deleteProject(@PathVariable String projectId,
                               Authentication authentication,
                               RedirectAttributes redirectAttributes) {

        boolean deleted = projectWebService.deleteProject(projectId, authentication, null);
        if (deleted) {
            redirectAttributes.addFlashAttribute("success", "Project deleted successfully!");
        } else {
            redirectAttributes.addFlashAttribute("error", "Failed to delete project!");
        }

        return "redirect:" + PathConstants.DASHBOARD;
    }

    @GetMapping(CommonPathConstants.PROJECT_MEMBERS_ENDPOINT)
    public String viewProjectMembers(@PathVariable String projectId,
                                   Authentication authentication,
                                   Model model) {

        projectWebService.prepareProjectView(projectId, authentication, model);
        model.addAttribute("memberRequest", new AddProjectMemberRequest());

        return CommonTemplateConstants.TEMPLATE_PROJECTS_MEMBERS;
    }

    @PostMapping(CommonPathConstants.PROJECT_MEMBERS_ENDPOINT)
    public String addProjectMember(@PathVariable String projectId,
                                  @Valid @ModelAttribute("memberRequest") AddProjectMemberRequest request,
                                  BindingResult result,
                                  Authentication authentication,
                                  Model model,
                                  RedirectAttributes redirectAttributes) {

        if (result.hasErrors()) {
            projectWebService.prepareProjectView(projectId, authentication, model);
            return CommonTemplateConstants.TEMPLATE_PROJECTS_MEMBERS;
        }

        boolean added = projectWebService.addProjectMember(projectId, request, authentication, model);
        if (added) {
            redirectAttributes.addFlashAttribute("success", "Member added successfully!");
        } else {
            redirectAttributes.addFlashAttribute("error", "Failed to add member!");
        }

        return CommonTemplateConstants.REDIRECT_PROJECTS_VIEW + projectId + CommonTemplateConstants.REDIRECT_PROJECTS_MEMBERS;
    }

    @PostMapping(CommonPathConstants.PROJECT_MEMBERS_ENDPOINT + CommonPathConstants.MEMBERS_REMOVE_ENDPOINT)
    public String removeProjectMember(@PathVariable String projectId,
                                     @PathVariable String userId,
                                     Authentication authentication,
                                     RedirectAttributes redirectAttributes) {

        boolean removed = projectWebService.removeProjectMember(projectId, userId, authentication, null);
        if (removed) {
            redirectAttributes.addFlashAttribute("success", "Member removed successfully!");
        } else {
            redirectAttributes.addFlashAttribute("error", "Failed to remove member!");
        }

        return CommonTemplateConstants.REDIRECT_PROJECTS_VIEW + projectId + CommonTemplateConstants.REDIRECT_PROJECTS_MEMBERS;
    }

    @ExceptionHandler(Exception.class)
    public String handleException(Exception e, RedirectAttributes redirectAttributes) {
        log.error("Error in ProjectWebController: ", e);
        redirectAttributes.addFlashAttribute("error", "An unexpected error occurred: " + e.getMessage());
        return "redirect:" + PathConstants.DASHBOARD;
    }
}
