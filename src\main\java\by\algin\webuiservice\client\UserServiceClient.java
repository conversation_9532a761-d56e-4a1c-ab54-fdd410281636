package by.algin.webuiservice.client;

import by.algin.dto.response.ApiResponse;
import by.algin.dto.response.UserResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * Wrapper service for UserService Feign client with error handling and Optional returns.
 */
@Slf4j
@Component
public class UserServiceClient {

    private final UserFeignClient feignClient;

    public UserServiceClient(UserFeignClient feignClient) {
        this.feignClient = feignClient;
    }

    public Optional<UserResponse> searchUsers(String field, String value) {
        try {
            UserResponse response = performUserSearch(field, value);
            return Optional.of(response);
        } catch (Exception e) {
            log.error("User search failed: field={}, value={}, error={}", field, value, e.getMessage());
            return Optional.empty();
        }
    }

    public UserResponse searchUsersOrThrow(String field, String value) {
        return performUserSearch(field, value);
    }

    private UserResponse performUserSearch(String field, String value) {
        log.info("Searching users: field={}, value={}", field, value);

        ApiResponse<UserResponse> apiResponse = feignClient.searchUsers(field, value);
        if (apiResponse.isSuccess() && apiResponse.getData() != null) {
            UserResponse response = apiResponse.getData();
            log.info("User search successful: {}", response);
            return response;
        } else {
            log.error("User search failed: {}", apiResponse.getMessage());
            throw new RuntimeException("User search failed: " + apiResponse.getMessage());
        }
    }

}
